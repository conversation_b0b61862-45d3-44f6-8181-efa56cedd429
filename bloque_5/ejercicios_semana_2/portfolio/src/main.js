import { createApp } from 'vue';

const app = createApp({
  data() {
    return {
      menuVisible: false,
      portfolio: null,
      test: 'hola'
    };
  },
  methods: {
    toggleMenu() {
      this.menuVisible = !this.menuVisible;
    }
  },
  async mounted() {
    try {
      // Load the main portfolio data
      const response = await fetch('/data.json');
      const data = await response.json();
      this.portfolio = data;
      console.log("Portfolio data loaded:", data);

      // Load the test data
      const testResponse = await fetch('/prueba.json');
      const testData = await testResponse.json();
      console.log("Test data loaded:", testData);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }
});

app.mount('#app');