<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artist Portfolio</title>
    <link href="/src/styles.css" rel="stylesheet">

   
</head>
<body>
    <div id="app">

   
    <header>
        <h1>{{ portfolio ? portfolio.name : 'Artist Name' }}</h1>
        <p>{{ portfolio ? portfolio.title : 'Creative Works & Portfolio' }}</p>
        <div class="burger" @click="toggleMenu()">☰</div>
        <div id="menu" class="menu" :style="{ display: menuVisible ? 'block' : 'none' }">
            <a href="#bio">About</a>
            <a href="#gallery">Gallery</a>
            <a href="#contact">Contact</a>
        </div>
    </header>
    


    <section id="bio" class="bio">
        <h2>About the Artist</h2>
        <p>{{ portfolio ? portfolio.bio : 'Brief biography and artistic statement.' }}</p>
    </section>
    
    <section v-if="portfolio" id="gallery" class="gallery">

  
            <a v-for="project in portfolio.projects" :key="project.id" :href="project.link">
                <img :src="project.image" :alt="project.name">
                <p>{{ project.name }}</p>
            </a>
           
    </section>
    
    <section id="contact" class="contact">
        <h2>Contact</h2>
        <p v-if="portfolio && portfolio.contact">Email: {{ portfolio.contact.email }}</p>
        <p v-else>Email: <EMAIL></p>
        <div v-if="portfolio && portfolio.contact">
            <a v-if="portfolio.contact.instagram" :href="portfolio.contact.instagram" target="_blank">Instagram</a>
            <a v-if="portfolio.contact.website" :href="portfolio.contact.website" target="_blank">Website</a>
        </div>
        <p v-else>Social Media Links</p>
    </section>
    
    <footer>
        <p>&copy; 2025 {{ portfolio ? portfolio.name : 'Artist Name' }}. All rights reserved.</p>
    </footer>

 
    <script type="module" src="/src/main.js"></script>

</div>
</body>
</html>
