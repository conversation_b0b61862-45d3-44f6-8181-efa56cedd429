<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Shader Example - Enhanced Pulsating Sphere</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body style="margin: 0; overflow: hidden;">
    <script>
        // Scene, Camera, and Renderer setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(renderer.domElement);

        // Create a directional light with shadows
        const light = new THREE.DirectionalLight(0xffffff, 1);
        light.position.set(5, 5, 5).normalize();
        scene.add(light);

        // Enable shadows for the renderer
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Create a ground plane that will receive shadows
        const groundGeometry = new THREE.PlaneGeometry(500, 500);
        const groundMaterial = new THREE.ShadowMaterial({ opacity: 0.5 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = - Math.PI / 2;
        ground.position.y = -2;
        ground.receiveShadow = true;
        scene.add(ground);

        // Vertex Shader: simple transformation
        const vertexShader = `
            varying vec3 vNormal;
            void main() {
                vNormal = normalize(normalMatrix * normal);
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;

        // Fragment Shader: pulsating effect with animated gradient pattern
        const fragmentShader = `
            uniform float time;
            varying vec3 vNormal;
            void main() {
                // Animated gradient colors based on time
                vec3 color1 = vec3(0.0, 0.5 + 0.5 * sin(time), 1.0);
                vec3 color2 = vec3(1.0, 0.0, 0.5 + 0.5 * cos(time));
                vec3 color = mix(color1, color2, 0.5 + 0.5 * sin(time * 0.5));

                // Adding some lighting to give the surface more depth
                float lightIntensity = max(dot(vNormal, normalize(vec3(1.0, 1.0, 1.0))), 0.0);
                gl_FragColor = vec4(color * lightIntensity, 1.0);
            }
        `;

        // Create a sphere geometry and shader material
        const geometry = new THREE.SphereGeometry(1, 64, 64); // Increased segments for more detail
        const material = new THREE.ShaderMaterial({
            vertexShader: vertexShader,
            fragmentShader: fragmentShader,
            uniforms: {
                time: { value: 0.0 }
            },
            side: THREE.FrontSide, // Front-facing polygons only
            flatShading: true
        });

        // Create the sphere mesh
        const sphere = new THREE.Mesh(geometry, material);
        sphere.castShadow = true; // Allow the sphere to cast shadows
        scene.add(sphere);

        // Position the camera
        camera.position.z = 3;

        // Resize handling
        window.addEventListener('resize', () => {
            renderer.setSize(window.innerWidth, window.innerHeight);
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
        });

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);

            // Update the time uniform to create pulsating effect
            material.uniforms.time.value += 0.05;

            // Rotate the sphere for more dynamic animation
            sphere.rotation.x += 0.02;
            sphere.rotation.y += 0.02;

            // Render the scene
            renderer.render(scene, camera);
        }

        animate();
    </script>
</body>
</html>
