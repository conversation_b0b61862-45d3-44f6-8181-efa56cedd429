let e,t,r,n,o;var i,s,a,u,f,l,c,h,p,d,y,g,m,b=globalThis;function w(e,t,r,n){Object.defineProperty(e,t,{get:r,set:n,enumerable:!0,configurable:!0})}function E(e,t){return function(){return e.apply(t,arguments)}}var v={},A=v={};function R(){throw Error("setTimeout has not been defined")}function O(){throw Error("clearTimeout has not been defined")}function T(e){if(c===setTimeout)return setTimeout(e,0);if((c===R||!c)&&setTimeout)return c=setTimeout,setTimeout(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}!function(){try{c="function"==typeof setTimeout?setTimeout:R}catch(e){c=R}try{h="function"==typeof clearTimeout?clearTimeout:O}catch(e){h=O}}();var S=[],U=!1,B=-1;function x(){U&&p&&(U=!1,p.length?S=p.concat(S):B=-1,S.length&&C())}function C(){if(!U){var e=T(x);U=!0;for(var t=S.length;t;){for(p=S,S=[];++B<t;)p&&p[B].run();B=-1,t=S.length}p=null,U=!1,function(e){if(h===clearTimeout)return clearTimeout(e);if((h===O||!h)&&clearTimeout)return h=clearTimeout,clearTimeout(e);try{h(e)}catch(t){try{return h.call(null,e)}catch(t){return h.call(this,e)}}}(e)}}function L(e,t){this.fun=e,this.array=t}function P(){}A.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];S.push(new L(e,t)),1!==S.length||U||T(C)},L.prototype.run=function(){this.fun.apply(null,this.array)},A.title="browser",A.browser=!0,A.env={},A.argv=[],A.version="",A.versions={},A.on=P,A.addListener=P,A.once=P,A.off=P,A.removeListener=P,A.removeAllListeners=P,A.emit=P,A.prependListener=P,A.prependOnceListener=P,A.listeners=function(e){return[]},A.binding=function(e){throw Error("process.binding is not supported")},A.cwd=function(){return"/"},A.chdir=function(e){throw Error("process.chdir is not supported")},A.umask=function(){return 0};const{toString:j}=Object.prototype,{getPrototypeOf:N}=Object,k=(e=Object.create(null),t=>{let r=j.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())}),_=e=>(e=e.toLowerCase(),t=>k(t)===e),F=e=>t=>typeof t===e,{isArray:I}=Array,D=F("undefined"),M=_("ArrayBuffer"),q=F("string"),z=F("function"),H=F("number"),J=e=>null!==e&&"object"==typeof e,W=e=>{if("object"!==k(e))return!1;let t=N(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},V=_("Date"),K=_("File"),$=_("Blob"),G=_("FileList"),X=_("URLSearchParams"),[Y,Q,Z,ee]=["ReadableStream","Request","Response","Headers"].map(_);function et(e,t,{allOwnKeys:r=!1}={}){let n,o;if(null!=e){if("object"!=typeof e&&(e=[e]),I(e))for(n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else{let o;let i=r?Object.getOwnPropertyNames(e):Object.keys(e),s=i.length;for(n=0;n<s;n++)o=i[n],t.call(null,e[o],o,e)}}}function er(e,t){let r;t=t.toLowerCase();let n=Object.keys(e),o=n.length;for(;o-- >0;)if(t===(r=n[o]).toLowerCase())return r;return null}const en="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:b,eo=e=>!D(e)&&e!==en,ei=(t="undefined"!=typeof Uint8Array&&N(Uint8Array),e=>t&&e instanceof t),es=_("HTMLFormElement"),ea=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),eu=_("RegExp"),ef=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),n={};et(r,(r,o)=>{let i;!1!==(i=t(r,o,e))&&(n[o]=i||r)}),Object.defineProperties(e,n)},el="abcdefghijklmnopqrstuvwxyz",ec="0123456789",eh={DIGIT:ec,ALPHA:el,ALPHA_DIGIT:el+el.toUpperCase()+ec},ep=_("AsyncFunction"),ed=(i="function"==typeof setImmediate,s=z(en.postMessage),i?setImmediate:s?(a=`axios@${Math.random()}`,u=[],en.addEventListener("message",({source:e,data:t})=>{e===en&&t===a&&u.length&&u.shift()()},!1),e=>{u.push(e),en.postMessage(a,"*")}):e=>setTimeout(e)),ey="undefined"!=typeof queueMicrotask?queueMicrotask.bind(en):void 0!==v&&v.nextTick||ed;var eg={isArray:I,isArrayBuffer:M,isBuffer:function(e){return null!==e&&!D(e)&&null!==e.constructor&&!D(e.constructor)&&z(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||z(e.append)&&("formdata"===(t=k(e))||"object"===t&&z(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&M(e.buffer)},isString:q,isNumber:H,isBoolean:e=>!0===e||!1===e,isObject:J,isPlainObject:W,isReadableStream:Y,isRequest:Q,isResponse:Z,isHeaders:ee,isUndefined:D,isDate:V,isFile:K,isBlob:$,isRegExp:eu,isFunction:z,isStream:e=>J(e)&&z(e.pipe),isURLSearchParams:X,isTypedArray:ei,isFileList:G,forEach:et,merge:function e(){let{caseless:t}=eo(this)&&this||{},r={},n=(n,o)=>{let i=t&&er(r,o)||o;W(r[i])&&W(n)?r[i]=e(r[i],n):W(n)?r[i]=e({},n):I(n)?r[i]=n.slice():r[i]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&et(arguments[e],n);return r},extend:(e,t,r,{allOwnKeys:n}={})=>(et(t,(t,n)=>{r&&z(t)?e[n]=E(t,r):e[n]=t},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,r,n)=>{let o,i,s;let a={};if(t=t||{},null==e)return t;do{for(i=(o=Object.getOwnPropertyNames(e)).length;i-- >0;)s=o[i],(!n||n(s,e,t))&&!a[s]&&(t[s]=e[s],a[s]=!0);e=!1!==r&&N(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype)return t},kindOf:k,kindOfTest:_,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;let n=e.indexOf(t,r);return -1!==n&&n===r},toArray:e=>{if(!e)return null;if(I(e))return e;let t=e.length;if(!H(t))return null;let r=Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{let r;let n=(e&&e[Symbol.iterator]).call(e);for(;(r=n.next())&&!r.done;){let n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let r;let n=[];for(;null!==(r=e.exec(t));)n.push(r);return n},isHTMLForm:es,hasOwnProperty:ea,hasOwnProp:ea,reduceDescriptors:ef,freezeMethods:e=>{ef(e,(t,r)=>{if(z(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(z(e[r])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(e,t)=>{let r={};return(e=>{e.forEach(e=>{r[e]=!0})})(I(e)?e:String(e).split(t)),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,r){return t.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:er,global:en,isContextDefined:eo,ALPHABET:eh,generateString:(e=16,t=eh.ALPHA_DIGIT)=>{let r="",{length:n}=t;for(;e--;)r+=t[Math.random()*n|0];return r},isSpecCompliantForm:function(e){return!!(e&&z(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let t=Array(10),r=(e,n)=>{if(J(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;let o=I(e)?[]:{};return et(e,(e,t)=>{let i=r(e,n+1);D(i)||(o[t]=i)}),t[n]=void 0,o}}return e};return r(e,0)},isAsyncFn:ep,isThenable:e=>e&&(J(e)||z(e))&&z(e.then)&&z(e.catch),setImmediate:ed,asap:ey};function em(e,t,r,n,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),o&&(this.response=o,this.status=o.status?o.status:null)}eg.inherits(em,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:eg.toJSONObject(this.config),code:this.code,status:this.status}}});const eb=em.prototype,ew={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ew[e]={value:e}}),Object.defineProperties(em,ew),Object.defineProperty(eb,"isAxiosError",{value:!0}),em.from=(e,t,r,n,o,i)=>{let s=Object.create(eb);return eg.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),em.call(s,e.message,t,r,n,o),s.cause=e,s.name=e.name,i&&Object.assign(s,i),s},d=function(e){var t,r,n=function(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}(e),o=n[0],i=n[1],s=new eA((o+i)*3/4-i),a=0,u=i>0?o-4:o;for(r=0;r<u;r+=4)t=ev[e.charCodeAt(r)]<<18|ev[e.charCodeAt(r+1)]<<12|ev[e.charCodeAt(r+2)]<<6|ev[e.charCodeAt(r+3)],s[a++]=t>>16&255,s[a++]=t>>8&255,s[a++]=255&t;return 2===i&&(t=ev[e.charCodeAt(r)]<<2|ev[e.charCodeAt(r+1)]>>4,s[a++]=255&t),1===i&&(t=ev[e.charCodeAt(r)]<<10|ev[e.charCodeAt(r+1)]<<4|ev[e.charCodeAt(r+2)]>>2,s[a++]=t>>8&255,s[a++]=255&t),s},y=function(e){for(var t,r=e.length,n=r%3,o=[],i=0,s=r-n;i<s;i+=16383)o.push(function(e,t,r){for(var n,o=[],i=t;i<r;i+=3)o.push(eE[(n=(e[i]<<16&0xff0000)+(e[i+1]<<8&65280)+(255&e[i+2]))>>18&63]+eE[n>>12&63]+eE[n>>6&63]+eE[63&n]);return o.join("")}(e,i,i+16383>s?s:i+16383));return 1===n?o.push(eE[(t=e[r-1])>>2]+eE[t<<4&63]+"=="):2===n&&o.push(eE[(t=(e[r-2]<<8)+e[r-1])>>10]+eE[t>>4&63]+eE[t<<2&63]+"="),o.join("")};for(var eE=[],ev=[],eA="undefined"!=typeof Uint8Array?Uint8Array:Array,eR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",eO=0,eT=eR.length;eO<eT;++eO)eE[eO]=eR[eO],ev[eR.charCodeAt(eO)]=eO;ev["-".charCodeAt(0)]=62,ev["_".charCodeAt(0)]=63,g=function(e,t,r,n,o){var i,s,a=8*o-n-1,u=(1<<a)-1,f=u>>1,l=-7,c=r?o-1:0,h=r?-1:1,p=e[t+c];for(c+=h,i=p&(1<<-l)-1,p>>=-l,l+=a;l>0;i=256*i+e[t+c],c+=h,l-=8);for(s=i&(1<<-l)-1,i>>=-l,l+=n;l>0;s=256*s+e[t+c],c+=h,l-=8);if(0===i)i=1-f;else{if(i===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),i-=f}return(p?-1:1)*s*Math.pow(2,i-n)},m=function(e,t,r,n,o,i){var s,a,u,f=8*i-o-1,l=(1<<f)-1,c=l>>1,h=23===o?5960464477539062e-23:0,p=n?0:i-1,d=n?1:-1,y=t<0||0===t&&1/t<0?1:0;for(isNaN(t=Math.abs(t))||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),s+c>=1?t+=h/u:t+=h*Math.pow(2,1-c),t*u>=2&&(s++,u/=2),s+c>=l?(a=0,s=l):s+c>=1?(a=(t*u-1)*Math.pow(2,o),s+=c):(a=t*Math.pow(2,c-1)*Math.pow(2,o),s=0));o>=8;e[r+p]=255&a,p+=d,a/=256,o-=8);for(s=s<<o|a,f+=o;f>0;e[r+p]=255&s,p+=d,s/=256,f-=8);e[r+p-d]|=128*y};var eS="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function eU(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,eB.prototype),t}function eB(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return eL(e)}return ex(e,t,r)}function ex(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!eB.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|ek(e,t),n=eU(r),o=n.write(e,t);return o!==r&&(n=n.slice(0,o)),n}(e,t);if(ArrayBuffer.isView(e))return function(e){if(eX(e,Uint8Array)){var t=new Uint8Array(e);return ej(t.buffer,t.byteOffset,t.byteLength)}return eP(e)}(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(eX(e,ArrayBuffer)||e&&eX(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(eX(e,SharedArrayBuffer)||e&&eX(e.buffer,SharedArrayBuffer)))return ej(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return eB.from(n,t,r);var o=function(e){if(eB.isBuffer(e)){var t,r=0|eN(e.length),n=eU(r);return 0===n.length||e.copy(n,0,0,r),n}return void 0!==e.length?"number"!=typeof e.length||(t=e.length)!=t?eU(0):eP(e):"Buffer"===e.type&&Array.isArray(e.data)?eP(e.data):void 0}(e);if(o)return o;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return eB.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function eC(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function eL(e){return eC(e),eU(e<0?0:0|eN(e))}function eP(e){for(var t=e.length<0?0:0|eN(e.length),r=eU(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function ej(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),eB.prototype),n}function eN(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function ek(e,t){if(eB.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||eX(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return eK(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return e$(e).length;default:if(o)return n?-1:eK(e).length;t=(""+t).toLowerCase(),o=!0}}function e_(e,t,r){var n,o,i=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var o="",i=t;i<r;++i)o+=eY[e[i]];return o}(this,t,r);case"utf8":case"utf-8":return eM(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(127&e[o]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(e[o]);return n}(this,t,r);case"base64":return n=t,o=r,0===n&&o===this.length?y(this):y(this.slice(n,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),o="",i=0;i<n.length-1;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}(this,t,r);default:if(i)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function eF(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function eI(e,t,r,n,o){var i;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(i=r=+r)!=i&&(r=o?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(o)return -1;r=e.length-1}else if(r<0){if(!o)return -1;r=0}if("string"==typeof t&&(t=eB.from(t,n)),eB.isBuffer(t))return 0===t.length?-1:eD(e,t,r,n,o);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?o?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):eD(e,[t],r,n,o);throw TypeError("val must be string, number or Buffer")}function eD(e,t,r,n,o){var i,s=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;s=2,a/=2,u/=2,r/=2}function f(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(o){var l=-1;for(i=r;i<a;i++)if(f(e,i)===f(t,-1===l?0:i-l)){if(-1===l&&(l=i),i-l+1===u)return l*s}else -1!==l&&(i-=i-l),l=-1}else for(r+u>a&&(r=a-u),i=r;i>=0;i--){for(var c=!0,h=0;h<u;h++)if(f(e,i+h)!==f(t,h)){c=!1;break}if(c)return i}return -1}function eM(e,t,r){r=Math.min(e.length,r);for(var n=[],o=t;o<r;){var i,s,a,u,f=e[o],l=null,c=f>239?4:f>223?3:f>191?2:1;if(o+c<=r)switch(c){case 1:f<128&&(l=f);break;case 2:(192&(i=e[o+1]))==128&&(u=(31&f)<<6|63&i)>127&&(l=u);break;case 3:i=e[o+1],s=e[o+2],(192&i)==128&&(192&s)==128&&(u=(15&f)<<12|(63&i)<<6|63&s)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:i=e[o+1],s=e[o+2],a=e[o+3],(192&i)==128&&(192&s)==128&&(192&a)==128&&(u=(15&f)<<18|(63&i)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,c=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),o+=c}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}function eq(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function ez(e,t,r,n,o,i){if(!eB.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<i)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function eH(e,t,r,n,o,i){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function eJ(e,t,r,n,o){return t=+t,r>>>=0,o||eH(e,t,r,4,34028234663852886e22,-34028234663852886e22),m(e,t,r,n,23,4),r+4}function eW(e,t,r,n,o){return t=+t,r>>>=0,o||eH(e,t,r,8,17976931348623157e292,-17976931348623157e292),m(e,t,r,n,52,8),r+8}eB.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),eB.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(eB.prototype,"parent",{enumerable:!0,get:function(){if(eB.isBuffer(this))return this.buffer}}),Object.defineProperty(eB.prototype,"offset",{enumerable:!0,get:function(){if(eB.isBuffer(this))return this.byteOffset}}),eB.poolSize=8192,eB.from=function(e,t,r){return ex(e,t,r)},Object.setPrototypeOf(eB.prototype,Uint8Array.prototype),Object.setPrototypeOf(eB,Uint8Array),eB.alloc=function(e,t,r){return(eC(e),e<=0)?eU(e):void 0!==t?"string"==typeof r?eU(e).fill(t,r):eU(e).fill(t):eU(e)},eB.allocUnsafe=function(e){return eL(e)},eB.allocUnsafeSlow=function(e){return eL(e)},eB.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==eB.prototype},eB.compare=function(e,t){if(eX(e,Uint8Array)&&(e=eB.from(e,e.offset,e.byteLength)),eX(t,Uint8Array)&&(t=eB.from(t,t.offset,t.byteLength)),!eB.isBuffer(e)||!eB.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,o=0,i=Math.min(r,n);o<i;++o)if(e[o]!==t[o]){r=e[o],n=t[o];break}return r<n?-1:n<r?1:0},eB.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},eB.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return eB.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=eB.allocUnsafe(t),o=0;for(r=0;r<e.length;++r){var i=e[r];if(eX(i,Uint8Array))o+i.length>n.length?eB.from(i).copy(n,o):Uint8Array.prototype.set.call(n,i,o);else if(eB.isBuffer(i))i.copy(n,o);else throw TypeError('"list" argument must be an Array of Buffers');o+=i.length}return n},eB.byteLength=ek,eB.prototype._isBuffer=!0,eB.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)eF(this,t,t+1);return this},eB.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)eF(this,t,t+3),eF(this,t+1,t+2);return this},eB.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)eF(this,t,t+7),eF(this,t+1,t+6),eF(this,t+2,t+5),eF(this,t+3,t+4);return this},eB.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?eM(this,0,e):e_.apply(this,arguments)},eB.prototype.toLocaleString=eB.prototype.toString,eB.prototype.equals=function(e){if(!eB.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===eB.compare(this,e)},eB.prototype.inspect=function(){var e="";return e=this.toString("hex",0,50).replace(/(.{2})/g,"$1 ").trim(),this.length>50&&(e+=" ... "),"<Buffer "+e+">"},eS&&(eB.prototype[eS]=eB.prototype.inspect),eB.prototype.compare=function(e,t,r,n,o){if(eX(e,Uint8Array)&&(e=eB.from(e,e.offset,e.byteLength)),!eB.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),t<0||r>e.length||n<0||o>this.length)throw RangeError("out of range index");if(n>=o&&t>=r)return 0;if(n>=o)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,o>>>=0,this===e)return 0;for(var i=o-n,s=r-t,a=Math.min(i,s),u=this.slice(n,o),f=e.slice(t,r),l=0;l<a;++l)if(u[l]!==f[l]){i=u[l],s=f[l];break}return i<s?-1:s<i?1:0},eB.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},eB.prototype.indexOf=function(e,t,r){return eI(this,e,t,r,!0)},eB.prototype.lastIndexOf=function(e,t,r){return eI(this,e,t,r,!1)},eB.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var o,i,s,a,u,f,l,c,h=this.length-t;if((void 0===r||r>h)&&(r=h),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var o=e.length-r;n?(n=Number(n))>o&&(n=o):n=o;var i=t.length;n>i/2&&(n=i/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(a!=a)break;e[r+s]=a}return s}(this,e,t,r);case"utf8":case"utf-8":return o=t,i=r,eG(eK(e,this.length-o),this,o,i);case"ascii":case"latin1":case"binary":return s=t,a=r,eG(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(e),this,s,a);case"base64":return u=t,f=r,eG(e$(e),this,u,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=t,c=r,eG(function(e,t){for(var r,n,o=[],i=0;i<e.length&&!((t-=2)<0);++i)n=(r=e.charCodeAt(i))>>8,o.push(r%256),o.push(n);return o}(e,this.length-l),this,l,c);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},eB.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},eB.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,eB.prototype),n},eB.prototype.readUintLE=eB.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||eq(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return n},eB.prototype.readUintBE=eB.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||eq(e,t,this.length);for(var n=this[e+--t],o=1;t>0&&(o*=256);)n+=this[e+--t]*o;return n},eB.prototype.readUint8=eB.prototype.readUInt8=function(e,t){return e>>>=0,t||eq(e,1,this.length),this[e]},eB.prototype.readUint16LE=eB.prototype.readUInt16LE=function(e,t){return e>>>=0,t||eq(e,2,this.length),this[e]|this[e+1]<<8},eB.prototype.readUint16BE=eB.prototype.readUInt16BE=function(e,t){return e>>>=0,t||eq(e,2,this.length),this[e]<<8|this[e+1]},eB.prototype.readUint32LE=eB.prototype.readUInt32LE=function(e,t){return e>>>=0,t||eq(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},eB.prototype.readUint32BE=eB.prototype.readUInt32BE=function(e,t){return e>>>=0,t||eq(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},eB.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||eq(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return n>=(o*=128)&&(n-=Math.pow(2,8*t)),n},eB.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||eq(e,t,this.length);for(var n=t,o=1,i=this[e+--n];n>0&&(o*=256);)i+=this[e+--n]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*t)),i},eB.prototype.readInt8=function(e,t){return(e>>>=0,t||eq(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},eB.prototype.readInt16LE=function(e,t){e>>>=0,t||eq(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},eB.prototype.readInt16BE=function(e,t){e>>>=0,t||eq(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},eB.prototype.readInt32LE=function(e,t){return e>>>=0,t||eq(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},eB.prototype.readInt32BE=function(e,t){return e>>>=0,t||eq(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},eB.prototype.readFloatLE=function(e,t){return e>>>=0,t||eq(e,4,this.length),g(this,e,!0,23,4)},eB.prototype.readFloatBE=function(e,t){return e>>>=0,t||eq(e,4,this.length),g(this,e,!1,23,4)},eB.prototype.readDoubleLE=function(e,t){return e>>>=0,t||eq(e,8,this.length),g(this,e,!0,52,8)},eB.prototype.readDoubleBE=function(e,t){return e>>>=0,t||eq(e,8,this.length),g(this,e,!1,52,8)},eB.prototype.writeUintLE=eB.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;ez(this,e,t,r,o,0)}var i=1,s=0;for(this[t]=255&e;++s<r&&(i*=256);)this[t+s]=e/i&255;return t+r},eB.prototype.writeUintBE=eB.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;ez(this,e,t,r,o,0)}var i=r-1,s=1;for(this[t+i]=255&e;--i>=0&&(s*=256);)this[t+i]=e/s&255;return t+r},eB.prototype.writeUint8=eB.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,1,255,0),this[t]=255&e,t+1},eB.prototype.writeUint16LE=eB.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},eB.prototype.writeUint16BE=eB.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},eB.prototype.writeUint32LE=eB.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},eB.prototype.writeUint32BE=eB.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},eB.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var o=Math.pow(2,8*r-1);ez(this,e,t,r,o-1,-o)}var i=0,s=1,a=0;for(this[t]=255&e;++i<r&&(s*=256);)e<0&&0===a&&0!==this[t+i-1]&&(a=1),this[t+i]=(e/s>>0)-a&255;return t+r},eB.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var o=Math.pow(2,8*r-1);ez(this,e,t,r,o-1,-o)}var i=r-1,s=1,a=0;for(this[t+i]=255&e;--i>=0&&(s*=256);)e<0&&0===a&&0!==this[t+i+1]&&(a=1),this[t+i]=(e/s>>0)-a&255;return t+r},eB.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},eB.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},eB.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},eB.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},eB.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||ez(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},eB.prototype.writeFloatLE=function(e,t,r){return eJ(this,e,t,!0,r)},eB.prototype.writeFloatBE=function(e,t,r){return eJ(this,e,t,!1,r)},eB.prototype.writeDoubleLE=function(e,t,r){return eW(this,e,t,!0,r)},eB.prototype.writeDoubleBE=function(e,t,r){return eW(this,e,t,!1,r)},eB.prototype.copy=function(e,t,r,n){if(!eB.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var o=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),o},eB.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!eB.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var o,i=e.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(e=i)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=eB.isBuffer(e)?e:eB.from(e,n),a=s.length;if(0===a)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(o=0;o<r-t;++o)this[o+t]=s[o%a]}return this};var eV=/[^+/0-9A-Za-z-_]/g;function eK(e,t){t=t||1/0;for(var r,n=e.length,o=null,i=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!o){if(r>56319||s+1===n){(t-=3)>-1&&i.push(239,191,189);continue}o=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),o=r;continue}r=(o-55296<<10|r-56320)+65536}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function e$(e){return d(function(e){if((e=(e=e.split("=")[0]).trim().replace(eV,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function eG(e,t,r,n){for(var o=0;o<n&&!(o+r>=t.length)&&!(o>=e.length);++o)t[o+r]=e[o];return o}function eX(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var eY=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,o=0;o<16;++o)t[n+o]=e[r]+e[o];return t}();function eQ(e){return eg.isPlainObject(e)||eg.isArray(e)}function eZ(e){return eg.endsWith(e,"[]")?e.slice(0,-2):e}function e0(e,t,r){return e?e.concat(t).map(function(e,t){return e=eZ(e),!r&&t?"["+e+"]":e}).join(r?".":""):t}const e1=eg.toFlatObject(eg,{},null,function(e){return/^is[A-Z]/.test(e)});var e2=function(e,t,r){if(!eg.isObject(e))throw TypeError("target must be an object");t=t||new FormData;let n=(r=eg.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!eg.isUndefined(t[e])})).metaTokens,o=r.visitor||f,i=r.dots,s=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&eg.isSpecCompliantForm(t);if(!eg.isFunction(o))throw TypeError("visitor must be a function");function u(e){if(null===e)return"";if(eg.isDate(e))return e.toISOString();if(!a&&eg.isBlob(e))throw new em("Blob is not supported. Use a Buffer instead.");return eg.isArrayBuffer(e)||eg.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):eB.from(e):e}function f(e,r,o){let a=e;if(e&&!o&&"object"==typeof e){if(eg.endsWith(r,"{}"))r=n?r:r.slice(0,-2),e=JSON.stringify(e);else{var f;if(eg.isArray(e)&&(f=e,eg.isArray(f)&&!f.some(eQ))||(eg.isFileList(e)||eg.endsWith(r,"[]"))&&(a=eg.toArray(e)))return r=eZ(r),a.forEach(function(e,n){eg.isUndefined(e)||null===e||t.append(!0===s?e0([r],n,i):null===s?r:r+"[]",u(e))}),!1}}return!!eQ(e)||(t.append(e0(o,r,i),u(e)),!1)}let l=[],c=Object.assign(e1,{defaultVisitor:f,convertValue:u,isVisitable:eQ});if(!eg.isObject(e))throw TypeError("data must be an object");return!function e(r,n){if(!eg.isUndefined(r)){if(-1!==l.indexOf(r))throw Error("Circular reference detected in "+n.join("."));l.push(r),eg.forEach(r,function(r,i){!0===(!(eg.isUndefined(r)||null===r)&&o.call(t,r,eg.isString(i)?i.trim():i,n,c))&&e(r,n?n.concat(i):[i])}),l.pop()}}(e),t};function e5(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function e8(e,t){this._pairs=[],e&&e2(e,this,t)}const e6=e8.prototype;function e3(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function e4(e,t,r){let n;if(!t)return e;let o=r&&r.encode||e3;eg.isFunction(r)&&(r={serialize:r});let i=r&&r.serialize;if(n=i?i(t,r):eg.isURLSearchParams(t)?t.toString():new e8(t,r).toString(o)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}e6.append=function(e,t){this._pairs.push([e,t])},e6.toString=function(e){let t=e?function(t){return e.call(this,t,e5)}:e5;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};var e9=class{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){eg.forEach(this.handlers,function(t){null!==t&&e(t)})}},e7={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},te="undefined"!=typeof URLSearchParams?URLSearchParams:e8,tt="undefined"!=typeof FormData?FormData:null,tr="undefined"!=typeof Blob?Blob:null,tn={};w(tn,"hasBrowserEnv",()=>to),w(tn,"navigator",()=>ti),w(tn,"hasStandardBrowserEnv",()=>ts),w(tn,"hasStandardBrowserWebWorkerEnv",()=>ta),w(tn,"origin",()=>tu);const to="undefined"!=typeof window&&"undefined"!=typeof document,ti="object"==typeof navigator&&navigator||void 0,ts=to&&(!ti||0>["ReactNative","NativeScript","NS"].indexOf(ti.product)),ta="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,tu=to&&window.location.href||"http://localhost";var tf={...tn,isBrowser:!0,classes:{URLSearchParams:te,FormData:tt,Blob:tr},protocols:["http","https","file","blob","url","data"]},tl=function(e){if(eg.isFormData(e)&&eg.isFunction(e.entries)){let t={};return eg.forEachEntry(e,(e,r)=>{!function e(t,r,n,o){let i=t[o++];if("__proto__"===i)return!0;let s=Number.isFinite(+i),a=o>=t.length;return(i=!i&&eg.isArray(n)?n.length:i,a)?eg.hasOwnProp(n,i)?n[i]=[n[i],r]:n[i]=r:(n[i]&&eg.isObject(n[i])||(n[i]=[]),e(t,r,n[i],o)&&eg.isArray(n[i])&&(n[i]=function(e){let t,r;let n={},o=Object.keys(e),i=o.length;for(t=0;t<i;t++)n[r=o[t]]=e[r];return n}(n[i]))),!s}(eg.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),r,t,0)}),t}return null};const tc={transitional:e7,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let r;let n=t.getContentType()||"",o=n.indexOf("application/json")>-1,i=eg.isObject(e);if(i&&eg.isHTMLForm(e)&&(e=new FormData(e)),eg.isFormData(e))return o?JSON.stringify(tl(e)):e;if(eg.isArrayBuffer(e)||eg.isBuffer(e)||eg.isStream(e)||eg.isFile(e)||eg.isBlob(e)||eg.isReadableStream(e))return e;if(eg.isArrayBufferView(e))return e.buffer;if(eg.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1){var s,a;return(s=e,a=this.formSerializer,e2(s,new tf.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,n){return tf.isNode&&eg.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},a))).toString()}if((r=eg.isFileList(e))||n.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return e2(r?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||o?(t.setContentType("application/json",!1),function(e,t,r){if(eg.isString(e))try{return(0,JSON.parse)(e),eg.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let t=this.transitional||tc.transitional,r=t&&t.forcedJSONParsing,n="json"===this.responseType;if(eg.isResponse(e)||eg.isReadableStream(e))return e;if(e&&eg.isString(e)&&(r&&!this.responseType||n)){let r=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!r&&n){if("SyntaxError"===e.name)throw em.from(e,em.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:tf.classes.FormData,Blob:tf.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};eg.forEach(["delete","get","head","post","put","patch"],e=>{tc.headers[e]={}});const th=eg.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);var tp=e=>{let t,r,n;let o={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),t=e.substring(0,n).trim().toLowerCase(),r=e.substring(n+1).trim(),!t||o[t]&&th[t]||("set-cookie"===t?o[t]?o[t].push(r):o[t]=[r]:o[t]=o[t]?o[t]+", "+r:r)}),o};const td=Symbol("internals");function ty(e){return e&&String(e).trim().toLowerCase()}function tg(e){return!1===e||null==e?e:eg.isArray(e)?e.map(tg):String(e)}const tm=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function tb(e,t,r,n,o){if(eg.isFunction(n))return n.call(this,t,r);if(o&&(t=r),eg.isString(t)){if(eg.isString(n))return -1!==t.indexOf(n);if(eg.isRegExp(n))return n.test(t)}}class tw{constructor(e){e&&this.set(e)}set(e,t,r){let n=this;function o(e,t,r){let o=ty(t);if(!o)throw Error("header name must be a non-empty string");let i=eg.findKey(n,o);i&&void 0!==n[i]&&!0!==r&&(void 0!==r||!1===n[i])||(n[i||t]=tg(e))}let i=(e,t)=>eg.forEach(e,(e,r)=>o(e,r,t));if(eg.isPlainObject(e)||e instanceof this.constructor)i(e,t);else if(eg.isString(e)&&(e=e.trim())&&!tm(e))i(tp(e),t);else if(eg.isHeaders(e))for(let[t,n]of e.entries())o(n,t,r);else null!=e&&o(t,e,r);return this}get(e,t){if(e=ty(e)){let r=eg.findKey(this,e);if(r){let e=this[r];if(!t)return e;if(!0===t)return function(e){let t;let r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=n.exec(e);)r[t[1]]=t[2];return r}(e);if(eg.isFunction(t))return t.call(this,e,r);if(eg.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=ty(e)){let r=eg.findKey(this,e);return!!(r&&void 0!==this[r]&&(!t||tb(this,this[r],r,t)))}return!1}delete(e,t){let r=this,n=!1;function o(e){if(e=ty(e)){let o=eg.findKey(r,e);o&&(!t||tb(r,r[o],o,t))&&(delete r[o],n=!0)}}return eg.isArray(e)?e.forEach(o):o(e),n}clear(e){let t=Object.keys(this),r=t.length,n=!1;for(;r--;){let o=t[r];(!e||tb(this,this[o],o,e,!0))&&(delete this[o],n=!0)}return n}normalize(e){let t=this,r={};return eg.forEach(this,(n,o)=>{let i=eg.findKey(r,o);if(i){t[i]=tg(n),delete t[o];return}let s=e?o.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r):String(o).trim();s!==o&&delete t[o],t[s]=tg(n),r[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return eg.forEach(this,(r,n)=>{null!=r&&!1!==r&&(t[n]=e&&eg.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let r=new this(e);return t.forEach(e=>r.set(e)),r}static accessor(e){let t=(this[td]=this[td]={accessors:{}}).accessors,r=this.prototype;function n(e){let n=ty(e);t[n]||(!function(e,t){let r=eg.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(e,r,o){return this[n].call(this,t,e,r,o)},configurable:!0})})}(r,e),t[n]=!0)}return eg.isArray(e)?e.forEach(n):n(e),this}}function tE(e,t){let r=this||tc,n=t||r,o=tw.from(n.headers),i=n.data;return eg.forEach(e,function(e){i=e.call(r,i,o.normalize(),t?t.status:void 0)}),o.normalize(),i}function tv(e){return!!(e&&e.__CANCEL__)}function tA(e,t,r){em.call(this,null==e?"canceled":e,em.ERR_CANCELED,t,r),this.name="CanceledError"}function tR(e,t,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new em("Request failed with status code "+r.status,[em.ERR_BAD_REQUEST,em.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}tw.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),eg.reduceDescriptors(tw.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}}),eg.freezeMethods(tw),eg.inherits(tA,em,{__CANCEL__:!0});var tO=function(e,t){let r;let n=Array(e=e||10),o=Array(e),i=0,s=0;return t=void 0!==t?t:1e3,function(a){let u=Date.now(),f=o[s];r||(r=u),n[i]=a,o[i]=u;let l=s,c=0;for(;l!==i;)c+=n[l++],l%=e;if((i=(i+1)%e)===s&&(s=(s+1)%e),u-r<t)return;let h=f&&u-f;return h?Math.round(1e3*c/h):void 0}},tT=function(e,t){let r,n,o=0,i=1e3/t,s=(t,i=Date.now())=>{o=i,r=null,n&&(clearTimeout(n),n=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),a=t-o;a>=i?s(e,t):(r=e,n||(n=setTimeout(()=>{n=null,s(r)},i-a)))},()=>r&&s(r)]};const tS=(e,t,r=3)=>{let n=0,o=tO(50,250);return tT(r=>{let i=r.loaded,s=r.lengthComputable?r.total:void 0,a=i-n,u=o(a);n=i,e({loaded:i,total:s,progress:s?i/s:void 0,bytes:a,rate:u||void 0,estimated:u&&s&&i<=s?(s-i)/u:void 0,event:r,lengthComputable:null!=s,[t?"download":"upload"]:!0})},r)},tU=(e,t)=>{let r=null!=e;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},tB=e=>(...t)=>eg.asap(()=>e(...t));var tx=tf.hasStandardBrowserEnv?(r=new URL(tf.origin),n=tf.navigator&&/(msie|trident)/i.test(tf.navigator.userAgent),e=>(e=new URL(e,tf.origin),r.protocol===e.protocol&&r.host===e.host&&(n||r.port===e.port))):()=>!0,tC=tf.hasStandardBrowserEnv?{write(e,t,r,n,o,i){let s=[e+"="+encodeURIComponent(t)];eg.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),eg.isString(n)&&s.push("path="+n),eg.isString(o)&&s.push("domain="+o),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function tL(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}const tP=e=>e instanceof tw?{...e}:e;function tj(e,t){t=t||{};let r={};function n(e,t,r,n){return eg.isPlainObject(e)&&eg.isPlainObject(t)?eg.merge.call({caseless:n},e,t):eg.isPlainObject(t)?eg.merge({},t):eg.isArray(t)?t.slice():t}function o(e,t,r,o){return eg.isUndefined(t)?eg.isUndefined(e)?void 0:n(void 0,e,r,o):n(e,t,r,o)}function i(e,t){if(!eg.isUndefined(t))return n(void 0,t)}function s(e,t){return eg.isUndefined(t)?eg.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function a(r,o,i){return i in t?n(r,o):i in e?n(void 0,r):void 0}let u={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t,r)=>o(tP(e),tP(t),r,!0)};return eg.forEach(Object.keys(Object.assign({},e,t)),function(n){let i=u[n]||o,s=i(e[n],t[n],n);eg.isUndefined(s)&&i!==a||(r[n]=s)}),r}var tN=e=>{let t;let r=tj({},e),{data:n,withXSRFToken:o,xsrfHeaderName:i,xsrfCookieName:s,headers:a,auth:u}=r;if(r.headers=a=tw.from(a),r.url=e4(tL(r.baseURL,r.url),e.params,e.paramsSerializer),u&&a.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),eg.isFormData(n)){if(tf.hasStandardBrowserEnv||tf.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(t=a.getContentType())){let[e,...r]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...r].join("; "))}}if(tf.hasStandardBrowserEnv&&(o&&eg.isFunction(o)&&(o=o(r)),o||!1!==o&&tx(r.url))){let e=i&&s&&tC.read(s);e&&a.set(i,e)}return r},tk="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,r){let n,o,i,s,a;let u=tN(e),f=u.data,l=tw.from(u.headers).normalize(),{responseType:c,onUploadProgress:h,onDownloadProgress:p}=u;function d(){s&&s(),a&&a(),u.cancelToken&&u.cancelToken.unsubscribe(n),u.signal&&u.signal.removeEventListener("abort",n)}let y=new XMLHttpRequest;function g(){if(!y)return;let n=tw.from("getAllResponseHeaders"in y&&y.getAllResponseHeaders());tR(function(e){t(e),d()},function(e){r(e),d()},{data:c&&"text"!==c&&"json"!==c?y.response:y.responseText,status:y.status,statusText:y.statusText,headers:n,config:e,request:y}),y=null}y.open(u.method.toUpperCase(),u.url,!0),y.timeout=u.timeout,"onloadend"in y?y.onloadend=g:y.onreadystatechange=function(){y&&4===y.readyState&&(0!==y.status||y.responseURL&&0===y.responseURL.indexOf("file:"))&&setTimeout(g)},y.onabort=function(){y&&(r(new em("Request aborted",em.ECONNABORTED,e,y)),y=null)},y.onerror=function(){r(new em("Network Error",em.ERR_NETWORK,e,y)),y=null},y.ontimeout=function(){let t=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded",n=u.transitional||e7;u.timeoutErrorMessage&&(t=u.timeoutErrorMessage),r(new em(t,n.clarifyTimeoutError?em.ETIMEDOUT:em.ECONNABORTED,e,y)),y=null},void 0===f&&l.setContentType(null),"setRequestHeader"in y&&eg.forEach(l.toJSON(),function(e,t){y.setRequestHeader(t,e)}),eg.isUndefined(u.withCredentials)||(y.withCredentials=!!u.withCredentials),c&&"json"!==c&&(y.responseType=u.responseType),p&&([i,a]=tS(p,!0),y.addEventListener("progress",i)),h&&y.upload&&([o,s]=tS(h),y.upload.addEventListener("progress",o),y.upload.addEventListener("loadend",s)),(u.cancelToken||u.signal)&&(n=t=>{y&&(r(!t||t.type?new tA(null,e,y):t),y.abort(),y=null)},u.cancelToken&&u.cancelToken.subscribe(n),u.signal&&(u.signal.aborted?n():u.signal.addEventListener("abort",n)));let m=function(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(u.url);if(m&&-1===tf.protocols.indexOf(m)){r(new em("Unsupported protocol "+m+":",em.ERR_BAD_REQUEST,e));return}y.send(f||null)})},t_=(e,t)=>{let{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,n=new AbortController,o=function(e){if(!r){r=!0,s();let t=e instanceof Error?e:this.reason;n.abort(t instanceof em?t:new tA(t instanceof Error?t.message:t))}},i=t&&setTimeout(()=>{i=null,o(new em(`timeout ${t} of ms exceeded`,em.ETIMEDOUT))},t),s=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)}),e=null)};e.forEach(e=>e.addEventListener("abort",o));let{signal:a}=n;return a.unsubscribe=()=>eg.asap(s),a}};const tF=function*(e,t){let r,n=e.byteLength;if(!t||n<t){yield e;return}let o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},tI=async function*(e,t){for await(let r of tD(e))yield*tF(r,t)},tD=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)break;yield r}}finally{await t.cancel()}},tM=(e,t,r,n)=>{let o;let i=tI(e,t),s=0,a=e=>{!o&&(o=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:t,value:n}=await i.next();if(t){a(),e.close();return}let o=n.byteLength;if(r){let e=s+=o;r(e)}e.enqueue(new Uint8Array(n))}catch(e){throw a(e),e}},cancel:e=>(a(e),i.return())},{highWaterMark:2})},tq="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tz=tq&&"function"==typeof ReadableStream,tH=tq&&("function"==typeof TextEncoder?(o=new TextEncoder,e=>o.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),tJ=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},tW=tz&&tJ(()=>{let e=!1,t=new Request(tf.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),tV=tz&&tJ(()=>eg.isReadableStream(new Response("").body)),tK={stream:tV&&(e=>e.body)};tq&&(f=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{tK[e]||(tK[e]=eg.isFunction(f[e])?t=>t[e]():(t,r)=>{throw new em(`Response type '${e}' is not supported`,em.ERR_NOT_SUPPORT,r)})}));const t$=async e=>{if(null==e)return 0;if(eg.isBlob(e))return e.size;if(eg.isSpecCompliantForm(e)){let t=new Request(tf.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return eg.isArrayBufferView(e)||eg.isArrayBuffer(e)?e.byteLength:(eg.isURLSearchParams(e)&&(e+=""),eg.isString(e))?(await tH(e)).byteLength:void 0},tG=async(e,t)=>{let r=eg.toFiniteNumber(e.getContentLength());return null==r?t$(t):r},tX={http:null,xhr:tk,fetch:tq&&(async e=>{let t,r,{url:n,method:o,data:i,signal:s,cancelToken:a,timeout:u,onDownloadProgress:f,onUploadProgress:l,responseType:c,headers:h,withCredentials:p="same-origin",fetchOptions:d}=tN(e);c=c?(c+"").toLowerCase():"text";let y=t_([s,a&&a.toAbortSignal()],u),g=y&&y.unsubscribe&&(()=>{y.unsubscribe()});try{if(l&&tW&&"get"!==o&&"head"!==o&&0!==(r=await tG(h,i))){let e,t=new Request(n,{method:"POST",body:i,duplex:"half"});if(eg.isFormData(i)&&(e=t.headers.get("content-type"))&&h.setContentType(e),t.body){let[e,n]=tU(r,tS(tB(l)));i=tM(t.body,65536,e,n)}}eg.isString(p)||(p=p?"include":"omit");let s="credentials"in Request.prototype;t=new Request(n,{...d,signal:y,method:o.toUpperCase(),headers:h.normalize().toJSON(),body:i,duplex:"half",credentials:s?p:void 0});let a=await fetch(t),u=tV&&("stream"===c||"response"===c);if(tV&&(f||u&&g)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=a[t]});let t=eg.toFiniteNumber(a.headers.get("content-length")),[r,n]=f&&tU(t,tS(tB(f),!0))||[];a=new Response(tM(a.body,65536,r,()=>{n&&n(),g&&g()}),e)}c=c||"text";let m=await tK[eg.findKey(tK,c)||"text"](a,e);return!u&&g&&g(),await new Promise((r,n)=>{tR(r,n,{data:m,headers:tw.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:t})})}catch(r){if(g&&g(),r&&"TypeError"===r.name&&/fetch/i.test(r.message))throw Object.assign(new em("Network Error",em.ERR_NETWORK,e,t),{cause:r.cause||r});throw em.from(r,r&&r.code,e,t)}})};eg.forEach(tX,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});const tY=e=>`- ${e}`,tQ=e=>eg.isFunction(e)||null===e||!1===e;var tZ=e=>{let t,r;let{length:n}=e=eg.isArray(e)?e:[e],o={};for(let i=0;i<n;i++){let n;if(r=t=e[i],!tQ(t)&&void 0===(r=tX[(n=String(t)).toLowerCase()]))throw new em(`Unknown adapter '${n}'`);if(r)break;o[n||"#"+i]=r}if(!r){let e=Object.entries(o).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new em("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(tY).join("\n"):" "+tY(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function t0(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new tA(null,e)}function t1(e){return t0(e),e.headers=tw.from(e.headers),e.data=tE.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),tZ(e.adapter||tc.adapter)(e).then(function(t){return t0(e),t.data=tE.call(e,e.transformResponse,t),t.headers=tw.from(t.headers),t},function(t){return!tv(t)&&(t0(e),t&&t.response&&(t.response.data=tE.call(e,e.transformResponse,t.response),t.response.headers=tw.from(t.response.headers))),Promise.reject(t)})}const t2="1.7.9",t5={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{t5[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const t8={};t5.transitional=function(e,t,r){function n(e,t){return"[Axios v"+t2+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,o,i)=>{if(!1===e)throw new em(n(o," has been removed"+(t?" in "+t:"")),em.ERR_DEPRECATED);return t&&!t8[o]&&(t8[o]=!0,console.warn(n(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,o,i)}},t5.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};var t6={assertOptions:function(e,t,r){if("object"!=typeof e)throw new em("options must be an object",em.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),o=n.length;for(;o-- >0;){let i=n[o],s=t[i];if(s){let t=e[i],r=void 0===t||s(t,i,e);if(!0!==r)throw new em("option "+i+" must be "+r,em.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new em("Unknown option "+i,em.ERR_BAD_OPTION)}},validators:t5};const t3=t6.validators;class t4{constructor(e){this.defaults=e,this.interceptors={request:new e9,response:new e9}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let r=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?r&&!String(e.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+r):e.stack=r}catch(e){}}throw e}}_request(e,t){let r,n;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:o,paramsSerializer:i,headers:s}=t=tj(this.defaults,t);void 0!==o&&t6.assertOptions(o,{silentJSONParsing:t3.transitional(t3.boolean),forcedJSONParsing:t3.transitional(t3.boolean),clarifyTimeoutError:t3.transitional(t3.boolean)},!1),null!=i&&(eg.isFunction(i)?t.paramsSerializer={serialize:i}:t6.assertOptions(i,{encode:t3.function,serialize:t3.function},!0)),t6.assertOptions(t,{baseUrl:t3.spelling("baseURL"),withXsrfToken:t3.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=s&&eg.merge(s.common,s[t.method]);s&&eg.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),t.headers=tw.concat(a,s);let u=[],f=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(f=f&&e.synchronous,u.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let c=0;if(!f){let e=[t1.bind(this),void 0];for(e.unshift.apply(e,u),e.push.apply(e,l),n=e.length,r=Promise.resolve(t);c<n;)r=r.then(e[c++],e[c++]);return r}n=u.length;let h=t;for(c=0;c<n;){let e=u[c++],t=u[c++];try{h=e(h)}catch(e){t.call(this,e);break}}try{r=t1.call(this,h)}catch(e){return Promise.reject(e)}for(c=0,n=l.length;c<n;)r=r.then(l[c++],l[c++]);return r}getUri(e){return e4(tL((e=tj(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}eg.forEach(["delete","get","head","options"],function(e){t4.prototype[e]=function(t,r){return this.request(tj(r||{},{method:e,url:t,data:(r||{}).data}))}}),eg.forEach(["post","put","patch"],function(e){function t(t){return function(r,n,o){return this.request(tj(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}t4.prototype[e]=t(),t4.prototype[e+"Form"]=t(!0)});class t9{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let r=this;this.promise.then(e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null}),this.promise.then=e=>{let t;let n=new Promise(e=>{r.subscribe(e),t=e}).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e(function(e,n,o){r.reason||(r.reason=new tA(e,n,o),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new t9(function(t){e=t}),cancel:e}}}const t7={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(t7).forEach(([e,t])=>{t7[t]=e});const re=function e(t){let r=new t4(t),n=E(t4.prototype.request,r);return eg.extend(n,t4.prototype,r,{allOwnKeys:!0}),eg.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return e(tj(t,r))},n}(tc);re.Axios=t4,re.CanceledError=tA,re.CancelToken=t9,re.isCancel=tv,re.VERSION=t2,re.toFormData=e2,re.AxiosError=em,re.Cancel=re.CanceledError,re.all=function(e){return Promise.all(e)},re.spread=function(e){return function(t){return e.apply(null,t)}},re.isAxiosError=function(e){return eg.isObject(e)&&!0===e.isAxiosError},re.mergeConfig=tj,re.AxiosHeaders=tw,re.formToJSON=e=>tl(eg.isHTMLForm(e)?new FormData(e):e),re.getAdapter=tZ,re.HttpStatusCode=t7,re.default=re;const{Axios:rt,AxiosError:rr,CanceledError:rn,isCancel:ro,CancelToken:ri,VERSION:rs,all:ra,Cancel:ru,isAxiosError:rf,spread:rl,toFormData:rc,AxiosHeaders:rh,HttpStatusCode:rp,formToJSON:rd,getAdapter:ry,mergeConfig:rg}=re,rm=async e=>{try{let t=await re.get("https://en.wikipedia.org/w/api.php",{params:{action:"query",format:"json",list:"search",srsearch:e,utf8:1,origin:"*"}});console.log(t.data),function(e){let t=e.query.search,r=document.getElementById("resultsContainer");r.innerHTML="",t.forEach(e=>{let t=e.title,n=`https://en.wikipedia.org/wiki/${encodeURIComponent(t)}`,o=document.createElement("div");o.innerHTML=`<a href="${n}" target="_blank">${t}</a>`,r.appendChild(o)})}(t.data)}catch(e){console.error("Error fetching data from Wikipedia API:",e)}},rb=(l="query",new URLSearchParams(window.location.search).get(l));rb&&rm(rb);
//# sourceMappingURL=index.b4ca9b9a.js.map
