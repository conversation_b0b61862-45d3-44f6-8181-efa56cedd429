body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    text-align: center;
    background: linear-gradient(to right, #ff9a9e, #fad0c4);
    color: #333;
}
header {
    background: #ff6f61;
    color: white;
    padding: 20px;
    position: relative;
}
.menu {
   
    background: #ff8a65;
    position: absolute;
    top: 60px;
    right: 10px;
    width: 150px;
    text-align: left;
}
.menu a {
    display: block;
    color: white;
    padding: 10px;
    text-decoration: none;
}
.menu a:hover {
    background: #ff7043;
}
.burger {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
    font-size: 24px;
}
.gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    padding: 20px;
}
.gallery a {
    text-decoration: none;
    color: black;
    background: #fff3e0;
    padding: 10px;
    border-radius: 8px;
    transition: transform 0.3s;
}
.gallery a:hover {
    transform: scale(1.05);
}
.gallery img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}
.bio, .contact {
    padding: 20px;
    background: #ffccbc;
    border-radius: 8px;
    margin: 20px;
}
footer {
    background: #ff6f61;
    color: white;
    padding: 10px;
    margin-top: 20px;
}