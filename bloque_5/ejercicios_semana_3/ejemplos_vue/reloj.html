<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reloj Digital</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <style>
    h1 {
      font-size: 3rem;
      font-family: 'Arial', sans-serif;
    }
  </style>
</head>
<body>

  <div id="app">
    <h1>{{ time }}</h1>
  </div>

  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          time: ''
        }
      },
      created() {
        setInterval(this.updateTime, 1000);
      },
      methods: {
        updateTime() {
          const now = new Date();
          this.time = now.toLocaleTimeString();
        }
      }
    });
  </script>

</body>
</html>
