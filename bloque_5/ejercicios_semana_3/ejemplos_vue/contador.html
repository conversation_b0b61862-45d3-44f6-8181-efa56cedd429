<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contador con Vue.js</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
</head>
<body>

  <div id="app">
    <h1>Contador: {{ count }}</h1>
    <button @click="increment">Incrementar</button>
    <button @click="decrement">Decrementar</button>
  </div>

  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          count: 0
        }
      },
      methods: {
        increment() {
          this.count++;
        },
        decrement() {
          this.count--;
        }
      }
    });
  </script>

</body>
</html>
