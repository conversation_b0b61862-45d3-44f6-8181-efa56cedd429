<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modal con Vue.js</title>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <style>
    /* Estilo para el modal */
    .modal {
     
      position: fixed;
      z-index: 1;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgb(0,0,0);
      background-color: rgba(0,0,0,0.4);
      padding-top: 60px;
    }

    .modal-content {
      background-color: #fefefe;
      margin: 5% auto;
      padding: 20px;
      border: 1px solid #888;
      width: 80%;
    }

    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
    }

    .close:hover,
    .close:focus {
      color: black;
      text-decoration: none;
      cursor: pointer;
    }
  </style>
</head>
<body>

  <div id="app">
    <!-- <PERSON>t<PERSON> para abrir el modal -->
    <button @click="openModal">Abrir <PERSON></button>

    <!-- Modal -->
    <div v-if="isModalOpen" class="modal">
      <div class="modal-content">
        <span class="close" @click="closeModal">&times;</span>
        <h2>Este es un Modal</h2>
        <p>Este es el contenido del modal. Puedes poner cualquier cosa aquí.</p>
        <button @click="closeModal">Cerrar</button>
      </div>
    </div>
  </div>

  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          isModalOpen: false
        }
      },
      methods: {
        openModal() {
           
          this.isModalOpen = true;
        },
        closeModal() {
          this.isModalOpen = false;
        }
      }
    });
  </script>

</body>
</html>
