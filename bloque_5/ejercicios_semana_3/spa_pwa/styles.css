body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }
  .nav {
    display: flex;
    background-color: #42b983;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  .nav a {
    color: white;
    padding: 10px 15px;
    text-decoration: none;
    margin-right: 10px;
    border-radius: 4px;
    transition: background-color 0.3s;
  }
  .nav a:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
  .nav a.router-link-active {
    background-color: rgba(255, 255, 255, 0.3);
    font-weight: bold;
  }
  .page {
    padding: 20px;
    border-radius: 4px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
  }
  .fade-enter, .fade-leave-to {
    opacity: 0;
  }
  .loading {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: #666;
  }
  .offline-banner {
    background-color: #ff6b6b;
    color: white;
    text-align: center;
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
    display: none;
  }