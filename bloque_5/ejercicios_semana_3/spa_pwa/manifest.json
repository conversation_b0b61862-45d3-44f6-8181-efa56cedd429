{"name": "Vue SPA PWA Example", "short_name": "Vue PWA", "description": "A simple Vue.js SPA with PWA capabilities", "start_url": "/", "display": "standalone", "orientation": "any", "background_color": "#42b983", "theme_color": "#42b983", "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png"}], "screenshots": [{"src": "/screenshots/home-screen.png", "sizes": "1280x720", "type": "image/png"}, {"src": "/screenshots/about-screen.png", "sizes": "1280x720", "type": "image/png"}], "shortcuts": [{"name": "About", "short_name": "About", "description": "View app information", "url": "/#/about", "icons": [{"src": "/icons/about-icon.png", "sizes": "192x192"}]}, {"name": "Contact", "short_name": "Contact", "description": "Contact us", "url": "/#/contact", "icons": [{"src": "/icons/contact-icon.png", "sizes": "192x192"}]}], "related_applications": [], "prefer_related_applications": false, "categories": ["productivity", "utilities"], "lang": "en-US", "dir": "ltr"}