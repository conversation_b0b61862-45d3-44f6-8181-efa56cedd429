<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue SPA - PWA Example</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/8.0.1/normalize.min.css">
<link rel="stylesheet" href="styles.css">
  <!-- PWA manifest link - point to external manifest.json -->
  <link rel="manifest" href="manifest.json">
  
  <!-- PWA related meta tags -->
  <meta name="theme-color" content="#42b983">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="Vue PWA">
  
  
</head>
<body>
  <div id="app">

    
    <div class="nav">
      <router-link to="/">Home</router-link>
      <router-link to="/about">About</router-link>
      <router-link to="/contact">Contact</router-link>
    </div>
    
    <transition name="fade" mode="out-in">
      <router-view></router-view>
    </transition>
  </div>

  <!-- Vue and Vue Router via CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.6.14/vue.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/vue-router/3.5.3/vue-router.min.js"></script>

  <script>
    // Define route components
    const Home = {
      template: `
        <div class="page">
          <h1>Home Page</h1>
          <p>Welcome to our Vue SPA with PWA capabilities!</p>
          <p>This is a simple example of a Single Page Application built with Vue.js and Vue Router.</p>
          <p>You can add this to your home screen as a PWA!</p>
        </div>
      `
    };

    const About = {
      template: `
        <div class="page">
          <h1>About Page</h1>
          <p>This is a demonstration of:</p>
          <ul>
            <li>Vue.js for reactive UI</li>
            <li>Vue Router for SPA navigation</li>
            <li>PWA manifest for installability</li>
            <li>All loaded via CDN (no npm)</li>
          </ul>
        </div>
      `
    };

    const Contact = {
      template: `
        <div class="page">
          <h1>Contact Page</h1>
          <p>You can reach us at:</p>
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Phone:</strong> (*************</p>
        </div>
      `
    };

    const NotFound = {
      template: `
        <div class="page">
          <h1>404 - Page Not Found</h1>
          <p>Sorry, the page you're looking for doesn't exist.</p>
          <router-link to="/">Go back to home</router-link>
        </div>
      `
    };

    // Define routes
    const routes = [
      { path: '/', component: Home },
      { path: '/about', component: About },
      { path: '/contact', component: Contact },
      { path: '*', component: NotFound }
    ];

    // Create router instance
    const router = new VueRouter({
      routes,
      //mode: 'history'
    });



    // Create Vue instance
    const app = new Vue({
      router
    }).$mount('#app');


  </script>
</body>
</html>