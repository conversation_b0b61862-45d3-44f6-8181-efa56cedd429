<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Simple PWA example">
    <link rel="manifest" href="manifest.json">
    <title>Simple PWA</title>
    <link rel="icon" href="images/icon.png">
</head>
<body>
    <h1>¡Hola! Esto es una PWA sencilla</h1>
    <p>Instala esta aplicación en tu dispositivo.</p>

    <button id="installButton">Instalar PWA</button>

    <script>
        let deferredPrompt;
        const installButton = document.getElementById('installButton');

        window.addEventListener('beforeinstallprompt', (e) => {
            // Previene que se muestre el banner de instalación automático
            e.preventDefault();
            deferredPrompt = e;
            installButton.style.display = 'block';

            installButton.addEventListener('click', () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('Usuario aceptó instalar');
                    } else {
                        console.log('Usuario rechazó la instalación');
                    }
                    deferredPrompt = null;
                });
            });
        });
    </script>

    <!-- Registro del Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('service-worker.js')
                .then(() => {
                    console.log('Service Worker registrado correctamente.');
                })
                .catch((error) => {
                    console.log('Error en el registro del Service Worker:', error);
                });
        }
    </script>
</body>
</html>
